import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";
import {
  Avatar,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
  Typography,
} from "@mui/material";
import React from "react";

interface PopupUpdateZaloProps {
  open: boolean;
  onClose: () => void;
  onOk: () => void;
}

const PopupUpdateZalo: React.FC<PopupUpdateZaloProps> = ({
  open,
  onClose,
  onOk,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          p: 0,
          overflow: "hidden",
        },
      }}
    >
      <Box
        sx={{
          p: 0,
          pt: 3,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
        }}
      >
        <IconButton
          onClick={onClose}
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            color: "#fff",
            zIndex: 1,
          }}
        >
          <CloseIcon />
        </IconButton>
        <Avatar sx={{ width: 64, height: 64, bgcolor: "#e3f0ff", mb: 1 }}>
          <span style={{ fontSize: 64 }}>😔</span>
        </Avatar>
        <Typography
          fontSize={20}
          fontWeight={700}
          color="black"
          textAlign="center"
        >
          Ôi không! Phiên bản đã cũ
        </Typography>
      </Box>
      <DialogContent sx={{ pb: 1, py: 1 }}>
        <Typography
          variant="body1"
          color="text.secondary"
          textAlign="center"
          lineHeight={1.6}
        >
          Phiên bản Zalo của bạn đã quá cũ và chúng tôi hiểu điều này có thể gây
          bất tiện. Hãy cập nhật để có trải nghiệm tốt nhất nhé!{" "}
          <span role="img" aria-label="sparkles">
            ✨
          </span>
        </Typography>
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3, pt: 1 }}>
        <Button
          onClick={onOk}
          variant="contained"
          startIcon={<DownloadIcon />}
          sx={(theme) => ({
            flex: 1,
            borderRadius: 2,
            background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            fontWeight: 600,
            fontSize: 18,
            py: 1.5,
            boxShadow: "none",
            textTransform: "none",
            "&:hover": {
              background: `linear-gradient(90deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
              boxShadow: "none",
            },
          })}
        >
          Cập nhật ngay
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PopupUpdateZalo;

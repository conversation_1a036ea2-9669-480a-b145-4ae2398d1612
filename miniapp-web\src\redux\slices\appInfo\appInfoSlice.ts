import { showToast } from "@/utils/common";
import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { request } from "../../../utils/request";

interface ShopInfo {
  partnerId?: string;
  oaId?: string;
  businessType?: string;
  shopName?: string;
  shopSlogan?: string;
  shopDesc?: string;
  shopInfo?: string;
  shopLogo?: any;
  shopDeeplink?: string;
  referralCode?: string;
  prefixCode?: string;
  startDate?: string;
  endDate?: string;
  openTime?: string;
  closeTime?: string;
  shipCost?: number;
  provinceId?: string;
  provinceName?: string;
  districtId?: string;
  districtName?: string;
  wardsId?: string;
  wardsName?: string;
  address?: string;
  transportPrice?: number;
  longitude?: number;
  latitude?: number;
  active?: string;
  status?: string;
  shopTheme?: {
    primaryColor?: string;
    secondaryColor?: string;
    accentColor?: string;
    bgPrimaryColor?: string;
    bgSecondaryColor?: string;
    bgAccentColor?: string;
    textPrimaryColor?: string;
    textSecondaryColor?: string;
    textAccentColor?: string;
  };
  shopPolicy?: any;
  template: {
    home: ObjectHome[];
    header: RemoteHeader;
    container: RemoteContainer;
    promotion?: ObjectHome[];
  };
  enableExpressDelivery?: boolean;
  enableInShop?: boolean;
}
interface DividerConfig {
  show?: boolean;
  color?: string;
}
interface ItemStyleHome {
  color?: string;
  textColor?: string;
  backgroundColor?: string;
  backgroundImage?: string;
  category: string;
  itemInRow: number;
  type?: string;
  row?: string;
  title?: string;
  moreText?: string;
  aspectRatio?: string;
  subtitle?: string;
  divider?: DividerConfig;
  sold?: boolean;
  discountType?: string;
  fontSize?: number;
  fontWeight?: number;
  lineHeight?: number;
}
export interface ObjectHome {
  show?: boolean;
  style?: ItemStyleHome;
  button?: {
    type?: string;
    text?: string;
  };
  type?: string;
  categoryId?: string;
  articleCategoryId?: string;
  articleIds?: string[];
  data?: string;
  title?: string;
  limit?: number;
  layout?: string;
}

export interface RemoteHeader {
  search?: {
    status: boolean;
    text?: string;
  };
  type?: string;
  backgroundColor?: string;
  backgroundImage?: string;
  alignLogo?: string;
}

export interface RemoteContainer {
  type?: string;
  backgroundColor?: string;
  backgroundImage?: string;
  navbar?: NavbarItem[];
}
interface NavbarItem {
  text: string;
  link: string;
  icon?: string;
  color?: string;
}
interface AppInfoState {
  shopId: string | null;
  shopInfo: ShopInfo;
  isLoading: boolean;
  error: string | null;
  isCampaignPopupShowed?: boolean | null;
}

const initialState: AppInfoState = {
  shopId: null,
  shopInfo: {
    template: {
      home: [],
      header: {},
      container: {},
    },
  },
  isLoading: true,
  error: null,
  isCampaignPopupShowed: null,
};

export const getShopId = createAsyncThunk(
  "shopId/get",
  async (
    data: {
      miniAppId?: string;
      domain?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      let shopId = "";
      if (data.domain) {
        const response: any = await request(
          "get",
          `/api/user/shopuser/GetShopIdFromDomain?domain=${data.domain}`
        );
        shopId = response.shopId;
      } else {
        const response: any = await request(
          "get",
          `/api/user/shopuser/getshopid?miniAppId=${data.miniAppId}`
        );
        shopId = response.shopId;
      }

      if (shopId) {
        const responseShop: any = await request(
          "get",
          `/api/user/shopuser/getshopinfo?shopId=${shopId}`
        );

        return responseShop;
      } else {
        showToast({
          content: "Đang cập nhập thông tin shop...",
          type: "error",
        });
        rejectWithValue({ message: "Chưa có thông tin Shop ID" });
      }
    } catch (error) {
      console.log(error);

      showToast({
        content: "Đang cập nhập thông tin shop...",
        type: "error",
      });
      rejectWithValue(error);
    }
  }
);

export const getShopInfo = createAsyncThunk(
  "shopInfo/get",
  async (
    data: {
      shopId: string;
    },
    { rejectWithValue }
  ) => {
    try {
      if (!data.shopId) {
        showToast({
          content: "Đang cập nhập thông tin shop...",
          type: "error",
        });
        rejectWithValue({ message: "chưa có thông tin shop" });
      }

      const response: any = await request(
        "get",
        `/api/user/shopuser/getshopinfo`
      );

      return response;
    } catch (error) {
      showToast({
        content: "Đang cập nhập thông tin shop...",
        type: "error",
      });
      rejectWithValue(error);
    }
  }
);

export const getShopTheme = createAsyncThunk(
  "shopTheme/get",
  async (
    data: {
      shopId: string;
    },
    { rejectWithValue }
  ) => {
    try {
      if (!data.shopId) {
        showToast({
          content: "Đang cập nhập thông tin shop...",
          type: "error",
        });
        rejectWithValue({ message: "Chưa có thông tin shop" });
      }

      const response: any = await request(
        "post",
        `/api/user/shopuser/getthemeshop?shopId=${data.shopId}`
      );

      return response;
    } catch (error) {
      showToast({
        content: "Đang cập nhập thông tin shop...",
        type: "error",
      });
      rejectWithValue(error);
    }
  }
);

export const getShopPolicy = createAsyncThunk(
  "shopPolicy/get",
  async (
    data: {
      shopId: string;
    },
    { rejectWithValue }
  ) => {
    try {
      if (!data.shopId) {
        showToast({
          content: "Đang cập nhập thông tin shop...",
          type: "error",
        });
        rejectWithValue({ message: "Chưa có thông tin shop" });
      }

      const response: any = await request(
        "get",
        `/api/user/shopuser/getshoppolicy?shopId=${data.shopId}`
      );

      return response;
    } catch (error) {
      showToast({
        content: "Đang cập nhập thông tin shop...1",
        type: "error",
      });
      rejectWithValue(error);
    }
  }
);

const appInfoSlice = createSlice({
  name: "appInfo",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // getShopId
      .addCase(getShopId.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getShopId.fulfilled, (state, action: PayloadAction<any>) => {
        if (action.payload?.shopId) {
          state.shopId = action.payload.shopId;
          state.shopInfo = { ...action.payload };
          state.isLoading = false;
        } else {
          // alert("Please check MiniAppId in .env file");
        }
      })
      .addCase(getShopId.rejected, (state, action) => {
        state.isLoading = false;
      })
      // getShopInfo
      .addCase(getShopInfo.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getShopInfo.fulfilled, (state, action: PayloadAction<any>) => {
        state.shopInfo = action.payload;
        state.isLoading = false;
      })
      .addCase(getShopInfo.rejected, (state, action) => {
        state.isLoading = false;
      })
      // getShopTheme
      .addCase(getShopTheme.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getShopTheme.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const data = action.payload;
          state.shopInfo = {
            ...state.shopInfo,
            oaId: data.oaId,
            shopLogo: data.logo,
            shopName: data.companyName,
            shopInfo: data.companyInfo,
            shopSlogan: data.slogan,
            openTime: data.openTime,
            closeTime: data.closeTime,
            shipCost: data.shipCost,
            prefixCode: data.prefixCode,
            shopTheme: {
              primaryColor: data.primaryColor,
              secondaryColor: data.secondaryColor,
              accentColor: data.accentColor,
            },
          };
          state.isLoading = false;
        }
      )
      .addCase(getShopTheme.rejected, (state, action) => {
        state.isLoading = false;
      })
      // getShopPolicy
      .addCase(getShopPolicy.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        getShopPolicy.fulfilled,
        (state, action: PayloadAction<Partial<any>>) => {
          const data = action.payload;
          state.shopInfo = {
            ...state.shopInfo,
            shopPolicy: data.shopPolicy,
          };
          state.isLoading = false;
          state.isLoading = false;
        }
      )
      .addCase(getShopPolicy.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export default appInfoSlice.reducer;

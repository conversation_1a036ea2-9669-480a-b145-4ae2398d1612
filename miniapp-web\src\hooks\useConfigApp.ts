import { RootState } from "@/redux/store";
import { useTheme } from "@mui/material/styles";
import { useSelector } from "react-redux";

export const useConfigApp = () => {
  const theme = useTheme();
  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  return {
    businessType: shopInfo.businessType,
    logo: shopInfo.shopLogo?.data?.attributes?.url,
    ...shopInfo,
    color: {
      primary: shopInfo.shopTheme?.primaryColor || theme.palette.primary,
      secondary:
        shopInfo.shopTheme?.secondaryColor || theme.palette.secondary.main,
      accent: shopInfo.shopTheme?.accentColor || theme.palette.primary.main,
    },
    bgColor: {
      primary:
        shopInfo.shopTheme?.bgPrimaryColor || theme.palette.background.default,
      secondary:
        shopInfo.shopTheme?.bgSecondaryColor ||
        theme.palette.background.default,
      accent:
        shopInfo.shopTheme?.bgAccentColor || theme.palette.background.paper,
    },
    textColor: {
      primary:
        shopInfo.shopTheme?.textPrimaryColor || theme.palette.text.primary,
      secondary:
        shopInfo.shopTheme?.textSecondaryColor || theme.palette.text.secondary,
      accent: shopInfo.shopTheme?.textAccentColor || theme.palette.text.primary,
      disable: theme.palette.text.disabled,
    },
    oaId: shopInfo.oaId || "",
    listProductStyle: "slice",
    ...shopInfo.template,
  };
};
